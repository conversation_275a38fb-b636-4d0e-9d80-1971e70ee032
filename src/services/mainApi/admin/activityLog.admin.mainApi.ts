import baseMainApi from "../base.mainApi";
import {
  AdminActivityLogListParams,
  AdminActivityLogListResponse,
} from "./types/activityLog.admin.mainApi.types";

export const activityLogAdminApi = {
  getActivityLogs: async (
    branchCode: string,
    params?: AdminActivityLogListParams
  ) => {
    const response =
      await baseMainApi.get<AdminActivityLogListResponse>(
        `/web-api/admin/${branchCode}/activity-log`,
        { params }
      );
    return response.data;
  },

  exportActivityLogs: async (
    branchCode: string,
    params: AdminActivityLogListParams,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/activity-log/export/${format}/${saveType}`,
      {
        params,
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },

  exportActivityLogById: async (
    branchCode: string,
    id: string,
    format: "pdf" | "spreadsheet",
    saveType: "link" | "buffer"
  ) => {
    const response = await baseMainApi.get(
      `/web-api/admin/${branchCode}/activity-log/export/${id}/${format}/${saveType}`,
      {
        responseType:
          saveType === "buffer" ? "arraybuffer" : "json",
      }
    );

    // If saveType is "buffer", convert the response data to a Blob
    if (saveType === "buffer") {
      const contentType =
        format === "pdf"
          ? "application/pdf"
          : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      return new Blob([response.data], {
        type: contentType,
      });
    }

    return response.data;
  },
};
