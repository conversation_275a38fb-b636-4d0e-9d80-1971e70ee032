import { BaseResponse } from "../../types/base.mainApi.types";
import { AdminActivity } from "./activity.admin.mainApi.types";
import { AdminRole } from "./roles.admin.mainApi.types";
import { AdminBranch } from "./branch.admin.mainApi.types";

// Timezone interface
interface AdminTimezone {
  id: string;
  timezone_name: string;
  gmt_offset: string;
  country_code: string;
  active: boolean;
}

// User interface specific to activity log
interface ActivityLogUser {
  id: string;
  parent_branch_id: string;
  role_id: string;
  name: string;
  email: string;
  phone: string;
  system_access: boolean;
  web_access: boolean;
  mobile_access: boolean;
  active: boolean;
  created_at: string;
  updated_at: string;
}

// Device interface for activity log (can be null)
interface ActivityLogDevice {
  id: string;
  device_name: string;

  // Allow additional properties of type string, number, or boolean
  [key: string]:
    | string
    | number
    | boolean
    | null
    | undefined;
}

export interface AdminActivityLogEntry {
  id: string;
  uuid: string;
  parent_branch_id: string;
  branch_id: string;
  branch_name: string;
  activity_id: string;
  activity_name: string;
  role_id: string;
  role_name: string;
  user_id: string;
  user_name: string;
  device_id: string | null;
  device_name: string | null;
  timezone_id: string | null;
  timezone_name: string | null;
  latitude: number | null;
  longitude: number | null;
  photo_url: string | null;
  photo_thumbnail_url: string | null;
  comment: string | null;
  original_submitted_time: string;
  event_time: string;

  // Relations
  branch: AdminBranch;
  activity: AdminActivity;
  role: AdminRole;
  user: ActivityLogUser;
  device: ActivityLogDevice | null;
  timezone: AdminTimezone | null;
}

export interface AdminActivityLogListParams {
  page?: number;
  limit?: number | null;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  branch_id?: string | number;
  role_id?: string | number;
  user_id?: string | number;
  user_labels?: string | number | (string | number)[];
  device_id?: string | number;
  device_labels?: string | number | (string | number)[];
  activity_id?: string | number;
  order_by?: string;
  order_direction?: "ASC" | "DESC";
}

export type AdminActivityLogListResponse = BaseResponse<
  AdminActivityLogEntry[]
>;
export type AdminActivityLogDetailResponse =
  BaseResponse<AdminActivityLogEntry>;
